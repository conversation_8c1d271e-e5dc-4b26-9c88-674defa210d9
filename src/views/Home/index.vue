<template>
  <div class="mx-auto flex flex-col" :style="{ height: 'calc(100vh - 90px)' }">
    <ElForm class="flex flex-col items-center flex-1 overflow-hidden">
      <div class="w-1/2 mb-3">
        <div class="flex flex-col items-center justify-center">
          <div class="w-full">
            <!-- 搜索类型和时间筛选器 -->
            <div class="mb-4 flex justify-start items-center gap-8">
              <ElRadioGroup v-model="esType" class="flex items-center">
                <ElRadio :value="1" size="large">精确搜索</ElRadio>
                <!-- <ElRadio :value="2" size="large">前缀搜索</ElRadio>
                <ElRadio :value="3" size="large">后缀搜索</ElRadio> -->
                <ElRadio :value="5" size="large">模糊搜索</ElRadio>
              </ElRadioGroup>

              <ElDatePicker
                v-model="searchDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD"
                class="fixed-width-date-picker"
                style="
                  width: 350px !important;
                  min-width: 350px !important;
                  max-width: 350px !important;
                "
                size="large"
                clearable
              />
            </div>

            <div style="display: flex; align-items: center; width: 100%">
              <ElInput
                v-model="keyword"
                placeholder="请输入关键词进行搜索"
                class="input-with-select"
                ref="searchInputRef"
                @click="onInputClick"
                @blur="onInputBlur"
              >
                <template #prepend>
                  <ElSelect
                    clearable=""
                    v-model="searchField"
                    placeholder="选择类型（必选项）"
                    style="width: 200px; margin-right: 20px"
                    size="large"
                  >
                    <ElOption
                      v-for="(item, key) in fields"
                      :key="key"
                      :value="item.key"
                      :label="item.name"
                    />
                  </ElSelect>
                  <ElSelect
                    multiple
                    collapse-tags
                    clearable=""
                    v-model="searchCountries"
                    placeholder="选择国家"
                    style="width: 150px; margin-right: 20px"
                    size="large"
                  >
                    <template #header>
                      <ElInput
                        placeholder="请输入关键字进行搜索"
                        v-model="countryInput"
                        @input="countryFilter"
                    /></template>
                    <ElOption
                      v-for="(item, key) in countriesList"
                      :key="key"
                      :value="item.id"
                      :label="`${item.name}${item.code}`"
                    >
                      <div class="flex justify-between">
                        <span>{{ item.name }}</span>
                        <span>{{ item.code }}</span>
                      </div>
                    </ElOption>
                  </ElSelect>
                  <ElSelect
                    multiple
                    collapse-tags
                    clearable=""
                    v-model="searchLanguages"
                    placeholder="选择语言"
                    style="width: 150px"
                    size="large"
                  >
                    <template #header>
                      <ElInput
                        placeholder="请输入关键字进行搜索"
                        v-model="languageInput"
                        @input="languageFilter"
                    /></template>
                    <ElOption
                      v-for="(item, key) in languagesList"
                      :key="key"
                      :value="item.id"
                      :label="item.name"
                    />
                  </ElSelect>
                </template>
                <template #append>
                  <ElButton size="large" @click="toSearch">搜索一下</ElButton>
                </template>
              </ElInput>
              <ElPopover
                v-model:visible="showRecordPopover"
                :virtual-ref="inputDomRef"
                virtual-triggering
                placement="bottom-start"
                width="600"
                trigger="manual"
                popper-class="recent-record-popover"
              >
                <template #default>
                  <div v-if="recentlySearchRecord && recentlySearchRecord.length > 0">
                    <div class="recent-record-header">
                      <span class="recent-record-title">最近搜索</span>
                      <ElButton
                        size="small"
                        text
                        @click="deleteRecentlySearchRecord"
                        class="delete-all-btn"
                      >
                        删除所有记录
                      </ElButton>
                    </div>
                    <div class="recent-record-list">
                      <div
                        v-for="(item, idx) in recentlySearchRecord"
                        :key="idx"
                        @mousedown.prevent="selectRecord(item)"
                        class="recent-record-item"
                      >
                        <span>{{ item.search_keyword }}</span>
                        <span v-if="item.searchField" class="search-field-tag"
                          >({{ item.searchField }})</span
                        >
                      </div>
                    </div>
                  </div>
                  <div v-else class="no-record-tip">暂无最近搜索</div>
                </template>
              </ElPopover>
            </div>
          </div>
        </div>
      </div>

      <div class="w-full mb-3">
        <ElRow :gutter="20" justify="space-between">
          <ElCol :span="12">
            <ElCard>
              <template #header>
                <div class="flex justify-between">
                  <div>最近新增数据</div>
                  <div>最近更新时间：{{ totalState.updated_at }}</div>
                </div>
              </template>
              <PanelGroup :totalState="totalState" />
            </ElCard>
          </ElCol>
          <ElCol :span="12">
            <ElCard>
              <template #header>
                <div class="flex">
                  <div>系统数据概览</div>
                  <!-- <div>数据总量：{{ sysData.allSizes }}</div> -->
                </div>
              </template>
              <PanelAll :totalState="sysData" />
            </ElCard>
          </ElCol>
        </ElRow>
      </div>

      <div class="w-full flex-1 min-h-0">
        <ElRow :gutter="20" justify="space-between" class="h-full">
          <ElCol :span="12" class="h-full">
            <ElCard class="h-full">
              <template #header>国家/地区PC量分布图</template>
              <div class="chart-container">
                <globalCountry />
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="12" class="h-full">
            <ElCard class="h-full">
              <template #header>国家/地区PC量Top10</template>
              <div class="chart-container">
                <topCountry />
              </div>
            </ElCard>
          </ElCol>
        </ElRow>
      </div>
    </ElForm>
  </div>
</template>

<script setup lang="jsx">
import {
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElForm,
  ElMessage,
  ElMessageBox,
  ElRow,
  ElCol,
  ElCard,
  ElTag,
  ElRadioGroup,
  ElRadio,
  ElAutocomplete,
  ElPopover,
  ElDatePicker
} from 'element-plus'
import { useIcon } from '@/hooks/web/useIcon'
import { computed, onMounted, reactive, ref, nextTick } from 'vue'
import { useRouter } from 'vue-router'
// import { searchTypes } from '@/constants/searchType'
import { ContentWrap } from '@/components/ContentWrap'
import { Descriptions } from '@/components/Descriptions'
import { CountTo } from '@/components/CountTo'
import PanelGroup from './components/panelGroup.vue'
import PanelAll from './components/panelAll.vue'

import globalCountry from './components/globalCountry.vue'
import topCountry from './components/topCountry.vue'

import {
  createSearchRecordApi,
  getRecentlySearchRecordApi,
  deleteRecentlySearchRecordApi
} from '@/api/searchRecord'
import { getDashboardApi } from '@/api/dashboard'
import { getCountryList, getLanguageList } from '@/utils/cache'
import { getFieldsApi } from '@/api/home'

import { useDictStore } from '@/store/modules/dict'

const dictStore = useDictStore()

// const formatSearchTypes = computed(() => {
//   return searchTypes.filter((item) => !item.hidden)
// })

// 最近
const totalState = ref({
  ipTotal: 0,
  passwordTotal: 0,
  sysTotal: 0,
  urlTotal: 0,
  fileTotal: 0,
  updated_at: null
})

// 总数
const sysData = ref({
  ipTotal: 0,
  passwordTotal: 0,
  sysTotal: 0,
  urlTotal: 0,
  allSize: null,
  fileTotal: 0
})

const sysSchema = reactive([
  {
    field: 'ftpTotal',
    label: 'FTP数量',
    width: '10%',
    slots: {
      default: ({ ftpTotal }) => {
        return <CountTo start-val={0} end-val={ftpTotal} duration={2600} />
      }
    }
  },
  {
    field: 'cookieTotal',
    label: 'Cookie数量',
    width: '10%',
    slots: {
      default: ({ cookieTotal }) => {
        return <CountTo start-val={0} end-val={cookieTotal} duration={2600} />
      }
    }
  },
  {
    field: 'passwordTotal',
    label: '密码数量',
    width: '10%',
    slots: {
      default: ({ passwordTotal }) => {
        return <CountTo start-val={0} end-val={passwordTotal} duration={2600} />
      }
    }
  },
  {
    field: 'sysTotal',
    label: '系统信息',
    width: '10%',
    slots: {
      default: ({ sysTotal }) => {
        return <CountTo start-val={0} end-val={sysTotal} duration={2600} />
      }
    }
  }
])

const esType = ref(1)

const router = useRouter()

const keyword = ref('')
// const searchType = ref([])
const searchCountries = ref([])
const searchLanguages = ref([])
const searchField = ref(null)
const searchDate = ref(null)

const Search = useIcon({ icon: 'ep:search' })

const countriesList = ref([])
const languagesList = ref([])

const fields = ref([])

const recentlySearchRecord = ref([])
const showRecordPopover = ref(false)
const searchInputRef = ref(null)
const inputDomRef = ref(null)

const toSearch = () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }
  if (keyword.value.length < 2) {
    ElMessage.error('关键字最小长度为2个字符')
    return
  }
  if (!searchField.value) {
    ElMessage.error('请选择搜索类型')
    return
  }

  if (searchField.value === 'email' && keyword.value.indexOf('@') === -1) {
    ElMessage.error('请输入正确的邮箱地址')
    return
  }

  const apiParams = {
    keyword: keyword.value,
    searchField: searchField.value,
    esType: esType.value,
    searchCountries: searchCountries.value,
    searchLanguages: searchLanguages.value,
    searchDate: searchDate.value,
    type: 1
  }
  // 记录日志
  createSearchRecordApi(apiParams)

  const resultTo = router.resolve({
    name: 'Result',
    query: apiParams
  })
  window.open(resultTo.href, '_blank')
}

const getDashboardData = async () => {
  const { data } = await getDashboardApi()
  totalState.value = data?.recent_total
  sysData.value = data?.total
}

const getRecentlySearchRecord = async () => {
  const { data } = await getRecentlySearchRecordApi()
  recentlySearchRecord.value = data
}

const deleteRecentlySearchRecord = async () => {
  try {
    await ElMessageBox.confirm('确定要删除所有搜索记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await deleteRecentlySearchRecordApi()
    recentlySearchRecord.value = []
    showRecordPopover.value = false
    ElMessage.success('删除成功')
  } catch (error) {
    // 用户取消删除
  }
}

const originCountriesList = ref([])
const originLanguagesList = ref([])

const getCountriesData = async () => {
  // const data = await getCountryList()
  const data = await dictStore.fetchCountryList()

  countriesList.value = data
  originCountriesList.value = data
}

const getLanguagesData = async () => {
  // const data = await getLanguageList()
  const data = await dictStore.fetchLanguageList()

  languagesList.value = data
  originLanguagesList.value = data
}

const countryInput = ref(null)
const countryFilter = (val) => {
  if (!val) {
    countriesList.value = originCountriesList.value
  } else {
    countriesList.value = countriesList.value.filter((item) => {
      return item.name.indexOf(val) > -1 || item.code.toUpperCase().indexOf(val.toUpperCase()) > -1
    })
  }
}

const languageInput = ref(null)
const languageFilter = (val) => {
  if (!val) {
    languagesList.value = originLanguagesList.value
  } else {
    languagesList.value = languagesList.value.filter((item) => {
      return item.name.indexOf(val) > -1
    })
  }
}

const getFields = async () => {
  const { data } = await getFieldsApi()
  fields.value = data
}

const onInputClick = () => {
  nextTick(() => {
    inputDomRef.value =
      searchInputRef.value && searchInputRef.value.input
        ? searchInputRef.value.input
        : searchInputRef.value?.$el?.querySelector('input') || null
    showRecordPopover.value = true
  })
}

const selectRecord = (item) => {
  keyword.value = item.search_keyword
  searchField.value = item.searchField || null
  showRecordPopover.value = false
}

const onInputBlur = () => {
  setTimeout(() => {
    showRecordPopover.value = false
  }, 120)
}

onMounted(() => {
  getDashboardData()
  getCountriesData()
  getLanguagesData()
  getFields()
  getRecentlySearchRecord()
})
</script>

<style scoped>
.img-container {
  max-height: 230px;
  background-image: url('@/assets/imgs/home.png');
  background-size: cover;
  background-position: center;
  width: 230px;
  height: 230px;
}
.input-with-select {
  width: 100%;
  border-radius: 24px;
  /* min-height: 50px; */
}
/* :deep(.el-select__wrapper) {
  min-height: 50px;
} */
/* :deep(.el-card__header) {
  padding: 10px;
} */
:deep(.el-card__body) {
  padding: 10px;
}

.container {
  margin: 0 auto;
  padding-top: 20px;
  min-width: 1000px;
}

:deep(.el-card) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-card__body) {
  flex: 1;
  padding: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chart-container {
  flex: 1;
  height: 100%;
  min-height: 0;
}

:deep(.el-skeleton) {
  height: 100%;
}

.recent-record-popover {
  background: #222 !important;
  color: #fff !important;
  border: 1px solid #444 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.recent-record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #333;
  border-bottom: 1px solid #444;
  margin: 0;
}

.recent-record-title {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.delete-all-btn {
  color: #ff6b6b !important;
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: auto !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.delete-all-btn:hover {
  background: rgba(255, 107, 107, 0.1) !important;
  color: #ff8a8a !important;
}

.recent-record-list {
  max-height: 300px;
  overflow-y: auto;
}

.recent-record-item {
  padding: 12px 16px;
  cursor: pointer;
  color: #fff;
  border-bottom: 1px solid #333;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.recent-record-item:last-child {
  border-bottom: none;
}

.recent-record-item:hover {
  background: #333;
  padding-left: 20px;
}

.search-field-tag {
  color: #aaa;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 8px;
}

.no-record-tip {
  padding: 20px 16px;
  color: #aaa;
  text-align: center;
  font-size: 14px;
}

/* 滚动条样式 */
.recent-record-list::-webkit-scrollbar {
  width: 4px;
}

.recent-record-list::-webkit-scrollbar-track {
  background: #333;
}

.recent-record-list::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 2px;
}

.recent-record-list::-webkit-scrollbar-thumb:hover {
  background: #666;
}
</style>
