<template>
  <ElDialog v-model="visible" :title="form.id ? '编辑代理池' : '添加代理池'" width="35%">
    <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <ElFormItem label="名称" prop="name">
        <ElInput v-model="form.name" />
      </ElFormItem>

      <ElFormItem label="代理列表" prop="count">
        <ElTransfer
          v-model="form.proxy_ids"
          filterable
          :filter-method="filterMethod"
          filter-placeholder="请输入代理名称"
          :titles="['代理列表', '已选代理']"
          :data="proxyList"
        />
      </ElFormItem>

      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="form.remark" type="textarea" :rows="3" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      <ElButton @click="visible = false">取消</ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
  ElTransfer
} from 'element-plus'
import { postProxyPoolApi, putProxyPoolApi } from '@/api/proxyPool'
import { getProxyListApi } from '@/api/proxy'

const emit = defineEmits(['success'])

const visible = ref(false)
const form = ref({})
const formRef = ref(null)
const rules = ref({
  name: [{ required: true, message: '请输入名称' }],
  proxy_ids: [{ required: true, message: '请选择代理' }]
})

const filterMethod = (query, item) => {
  return item.label.toLowerCase().includes(query.toLowerCase())
}

const proxyList = ref([])

const getProxyList = async () => {
  const res = await getProxyListApi({
    paging: 0
  })
  proxyList.value = res.data.map((item) => ({
    key: item.id,
    label: item.name
  }))
}

const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (form.value.id) {
        await putProxyPoolApi(form.value.id, form.value)
      } else {
        await postProxyPoolApi(form.value)
      }
      visible.value = false
      ElMessage.success('操作成功')
      emit('success')
    }
  })
}

const open = (data = {}) => {
  getProxyList()

  form.value = {
    ...data,
    proxy_ids: data?.proxies?.map((item) => item.id) || []
  }
  formRef.value?.resetFields()
  visible.value = true
}

defineExpose({
  open
})
</script>
