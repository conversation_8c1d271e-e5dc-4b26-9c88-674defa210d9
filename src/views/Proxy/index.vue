<template>
  <ElTabs v-model="activeTab">
    <ElTabPane label="代理列表" name="list">
      <List />
    </ElTabPane>
    <ElTabPane label="代理池" name="pool">
      <Pool />
    </ElTabPane>
  </ElTabs>
</template>

<script setup>
import { ref } from 'vue'
import List from './ProxyList/index.vue'
import Pool from './ProxyPool/index.vue'

import { ElTabs, ElTabPane } from 'element-plus'

const activeTab = ref('list')
</script>
