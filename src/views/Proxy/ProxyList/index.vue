<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { BaseButton } from '@/components/Button'

import { ElTag, ElDivider, ElMessageBox, ElMessage, ElButton } from 'element-plus'
import { FormSchema } from '@/components/Form'

import { filterProxyMode, filterProxyType } from '@/utils/filter'
import { getProxyListApi, deleteProxyApi } from '@/api/proxy'

import Form from './components/form.vue'
import Batch from './components/batch.vue'

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getProxyListApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'name',
    label: '名称',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'mode',
    label: '代理方式',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterProxyMode(data.row.mode, true)}>
              {filterProxyMode(data.row.mode)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'type',
    label: '代理类型',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterProxyType(data.row.type, true)}>
              {filterProxyType(data.row.type)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'host',
    label: '主机',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'port',
    label: '端口',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'username',
    label: '用户名',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'password',
    label: '密码',
    minWidth: '160',
    search: {
      hidden: true
    }
  },

  {
    field: 'created_at',
    label: '创建时间',
    minWidth: '160',
    search: {
      hidden: true
    }
  },

  {
    field: 'action',
    label: '操作',
    search: {
      hidden: true
    },
    fixed: 'right',
    width: '150',
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            <BaseButton type="primary" size="small" onClick={() => handleEdit(row)}>
              编辑
            </BaseButton>
            <BaseButton type="danger" size="small" onClick={() => deleteRecord(row)}>
              删除
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchSchemas = reactive<FormSchema[]>([
  {
    field: 'name',
    label: '名称',
    component: 'Input'
  },

  {
    field: 'mode',
    label: '代理方式',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '默认',
          value: 1
        },
        {
          label: '自建',
          value: 2
        }
      ]
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const handleEdit = (row: any) => {
  formRef.value.open?.({
    ...row
  })
}

const deleteRecord = (row: any) => {
  ElMessageBox.confirm('确定删除该代理吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await deleteProxyApi(row.id)
    ElMessage.success('删除成功')
    getList()
  })
}

const formRef = ref()
const batchRef = ref()

const handleAdd = () => {
  formRef.value.open?.()
}

const handleBatch = () => {
  batchRef.value.open?.()
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchemas" @reset="setSearchParams" @search="setSearchParams" />

    <ElDivider />

    <ElButton class="mb-4" type="primary" @click="handleAdd">添加代理</ElButton>
    <ElButton class="mb-4" type="primary" @click="handleBatch">批量添加代理</ElButton>
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />

    <Form ref="formRef" @success="getList" />
    <Batch ref="batchRef" @success="getList" />
  </ContentWrap>
</template>
