<template>
  <ElDialog v-model="visible" title="批量添加" width="35%">
    <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <ElFormItem label="代理方式" prop="mode">
        <ElSelect v-model="form.mode" placeholder="请选择代理方式">
          <ElOption label="默认" :value="1" />
          <ElOption label="自建" :value="2" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="代理类型" prop="type">
        <ElSelect v-model="form.type" placeholder="请选择代理类型">
          <ElOption label="HTTPS" :value="1" />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="代理列表" prop="proxyList">
        <ElInput v-model="form.proxyList" type="textarea" :rows="6" />
        <div>
          说明：
          <br />
          1. 一行一个代理，一次最多添加50个；
          <br />
          2. 格式为：代理账号:代理密码:代理IP:代理端口，如：username:password:***********:8000
        </div>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      <ElButton @click="visible = false">取消</ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage
} from 'element-plus'
import { postProxyBatchApi } from '@/api/proxy'

const emit = defineEmits(['success'])

const visible = ref(false)
const form = ref({})
const formRef = ref(null)
const rules = ref({
  mode: [{ required: true, message: '请选择代理方式' }],
  type: [{ required: true, message: '请选择代理类型' }],
  proxyList: [{ required: true, message: '请输入代理列表' }]
})

const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      // 校验代理列表
      const proxyList = form.value.proxyList.split('\n')
      if (proxyList.length > 50) {
        ElMessage.error('代理列表最多50个')
        return
      }

      // 校验代理列表格式
      for (const proxy of proxyList) {
        const [username, password, host, port] = proxy.split(':')
        if (!username || !password || !host || !port) {
          ElMessage.error('代理列表格式错误')
          return
        }
      }

      await postProxyBatchApi(form.value)
      visible.value = false
      ElMessage.success('操作成功')
      emit('success')
    }
  })
}

const open = (data = {}) => {
  form.value = data
  formRef.value?.resetFields()
  visible.value = true
}

defineExpose({
  open
})
</script>
