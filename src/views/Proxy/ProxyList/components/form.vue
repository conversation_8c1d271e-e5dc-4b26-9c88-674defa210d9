<template>
  <ElDialog v-model="visible" :title="form.id ? '编辑代理' : '添加代理'" width="35%">
    <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <ElFormItem label="名称" prop="name">
        <ElInput v-model="form.name" />
      </ElFormItem>
      <ElFormItem label="代理方式" prop="mode">
        <ElSelect v-model="form.mode" placeholder="请选择代理方式">
          <ElOption label="默认" :value="1" />
          <ElOption label="自建" :value="2" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="代理类型" prop="type">
        <ElSelect v-model="form.type" placeholder="请选择代理类型">
          <ElOption label="HTTPS" :value="1" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="主机" prop="host">
        <ElInput v-model="form.host" />
      </ElFormItem>
      <ElFormItem label="端口" prop="port">
        <ElInput v-model="form.port" />
      </ElFormItem>
      <ElFormItem label="用户名" prop="username">
        <ElInput v-model="form.username" />
      </ElFormItem>
      <ElFormItem label="密码" prop="password">
        <ElInput v-model="form.password" />
      </ElFormItem>
      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="form.remark" type="textarea" :rows="3" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="visible = false">取消</ElButton>
      <ElButton :loading="ipInfoLoading" type="success" @click="handleIpInfo">检查代理</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </ElDialog>

  <!-- IP信息弹框 -->
  <ElDialog v-model="ipInfoVisible" title="代理IP信息" width="400px" append-to-body>
    <div v-if="ipInfo" class="ip-info-content">
      <div class="info-item">
        <span class="label">IP地址：</span>
        <span class="value">{{ ipInfo.ip }}</span>
      </div>
      <div class="info-item">
        <span class="label">国家：</span>
        <span class="value">{{ ipInfo.country }}</span>
      </div>
      <div class="info-item">
        <span class="label">地区：</span>
        <span class="value">{{ ipInfo.region }}</span>
      </div>
      <div class="info-item">
        <span class="label">城市：</span>
        <span class="value">{{ ipInfo.city }}</span>
      </div>
    </div>
    <template #footer>
      <ElButton type="primary" @click="ipInfoVisible = false">确定</ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage
} from 'element-plus'
import { postIpInfoApi, postProxyApi, putProxyApi } from '@/api/proxy'

const emit = defineEmits(['success'])

const visible = ref(false)
const form = ref({})
const formRef = ref(null)
const ipInfoLoading = ref(false)
const ipInfoVisible = ref(false)
const ipInfo = ref(null)

const rules = ref({
  name: [{ required: true, message: '请输入名称' }],
  mode: [{ required: true, message: '请选择代理方式' }],
  type: [{ required: true, message: '请选择代理类型' }],
  host: [{ required: true, message: '请输入主机' }],
  port: [{ required: true, message: '请输入端口' }]
})

const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (form.value.id) {
        await putProxyApi(form.value.id, form.value)
      } else {
        await postProxyApi(form.value)
      }
      visible.value = false
      ElMessage.success('操作成功')
      emit('success')
    }
  })
}

const handleIpInfo = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        ipInfoLoading.value = true
        const { data } = await postIpInfoApi(form.value)
        ipInfoLoading.value = false

        // 显示IP信息弹框
        if (data && data.ip_info) {
          ipInfo.value = data.ip_info
          ipInfoVisible.value = true
        } else {
          ElMessage.warning('未获取到IP信息')
        }
      } catch (error) {
        ipInfoLoading.value = false
        ElMessage.error('获取IP信息失败')
      }
    }
  })
}

const open = (data = {}) => {
  form.value = data
  formRef.value?.resetFields()
  visible.value = true
}

defineExpose({
  open
})
</script>

<style scoped>
.ip-info-content {
  padding: 10px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #303133;
  font-weight: 600;
}
</style>
