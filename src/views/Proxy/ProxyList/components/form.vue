<template>
  <ElDialog v-model="visible" :title="form.id ? '编辑代理' : '添加代理'" width="35%">
    <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <ElFormItem label="名称" prop="name">
        <ElInput v-model="form.name" />
      </ElFormItem>
      <ElFormItem label="代理方式" prop="mode">
        <ElSelect v-model="form.mode" placeholder="请选择代理方式">
          <ElOption label="默认" :value="1" />
          <ElOption label="自建" :value="2" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="代理类型" prop="type">
        <ElSelect v-model="form.type" placeholder="请选择代理类型">
          <ElOption label="HTTPS" :value="1" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="主机" prop="host">
        <ElInput v-model="form.host" />
      </ElFormItem>
      <ElFormItem label="端口" prop="port">
        <ElInput v-model="form.port" />
      </ElFormItem>
      <ElFormItem label="用户名" prop="username">
        <ElInput v-model="form.username" />
      </ElFormItem>
      <ElFormItem label="密码" prop="password">
        <ElInput v-model="form.password" />
      </ElFormItem>
      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="form.remark" type="textarea" :rows="3" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton @click="visible = false">取消</ElButton>
      <ElButton :loading="ipInfoLoading" type="success" @click="handleIpInfo">检查代理</ElButton>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage
} from 'element-plus'
import { postIpInfoApi, postProxyApi, putProxyApi } from '@/api/proxy'

const emit = defineEmits(['success'])

const visible = ref(false)
const form = ref({})
const formRef = ref(null)
const ipInfoLoading = ref(false)

const rules = ref({
  name: [{ required: true, message: '请输入名称' }],
  mode: [{ required: true, message: '请选择代理方式' }],
  type: [{ required: true, message: '请选择代理类型' }],
  host: [{ required: true, message: '请输入主机' }],
  port: [{ required: true, message: '请输入端口' }]
})

const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (form.value.id) {
        await putProxyApi(form.value.id, form.value)
      } else {
        await postProxyApi(form.value)
      }
      visible.value = false
      ElMessage.success('操作成功')
      emit('success')
    }
  })
}

const handleIpInfo = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      ipInfoLoading.value = true
      const res = await postIpInfoApi(form.value)
      ipInfoLoading.value = false
      console.log(res)
    }
  })
}

const open = (data = {}) => {
  form.value = data
  formRef.value?.resetFields()
  visible.value = true
}

defineExpose({
  open
})
</script>
