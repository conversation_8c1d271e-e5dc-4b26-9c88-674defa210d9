<template>
  <ElDialog v-model="visible" :title="form.id ? '编辑UA' : '添加UA'" width="35%">
    <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <ElFormItem label="名称" prop="name">
        <ElInput v-model="form.name" />
      </ElFormItem>

      <ElFormItem label="UA" prop="content">
        <ElInput v-model="form.content" type="textarea" :rows="3" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      <ElButton @click="visible = false">取消</ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage
} from 'element-plus'
import { postUaApi, putUaApi } from '@/api/ua'

const emit = defineEmits(['success'])

const visible = ref(false)
const form = ref({})
const formRef = ref(null)
const rules = ref({
  name: [{ required: true, message: '请输入名称' }],
  content: [{ required: true, message: '请输入UA' }]
})

const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (form.value.id) {
        await putUaApi(form.value.id, form.value)
      } else {
        await postUaApi(form.value)
      }
      visible.value = false
      ElMessage.success('操作成功')
      emit('success')
    }
  })
}

const open = (data = {}) => {
  form.value = data
  formRef.value?.resetFields()
  visible.value = true
}

defineExpose({
  open
})
</script>
