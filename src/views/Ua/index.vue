<template>
  <ElTabs v-model="activeTab">
    <ElTabPane label="UA列表" name="list">
      <List />
    </ElTabPane>
    <ElTabPane label="UA池" name="pool">
      <Pool />
    </ElTabPane>
  </ElTabs>
</template>

<script setup>
import { ref } from 'vue'
import List from './UaList/index.vue'
import Pool from './UaPool/index.vue'

import { ElTabs, ElTabPane } from 'element-plus'

const activeTab = ref('list')
</script>
