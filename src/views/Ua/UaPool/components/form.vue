<template>
  <ElDialog v-model="visible" :title="form.id ? '编辑UA池' : '添加UA池'" width="35%">
    <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <ElFormItem label="名称" prop="name">
        <ElInput v-model="form.name" />
      </ElFormItem>

      <ElFormItem label="UA列表" prop="ua_ids">
        <ElTransfer
          v-model="form.ua_ids"
          filterable
          :filter-method="filterMethod"
          filter-placeholder="请输入UA名称"
          :titles="['UA列表', '已选UA']"
          :data="uaList"
        />
      </ElFormItem>

      <ElFormItem label="备注" prop="remark">
        <ElInput v-model="form.remark" type="textarea" :rows="3" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      <ElButton @click="visible = false">取消</ElButton>
    </template>
  </ElDialog>
</template>

<script setup>
import { ref, defineExpose, defineEmits } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
  ElTransfer
} from 'element-plus'
import { postUaPoolApi, putUaPoolApi } from '@/api/uaPool'
import { getUaListApi } from '@/api/ua'

const emit = defineEmits(['success'])

const visible = ref(false)
const form = ref({})
const formRef = ref(null)
const rules = ref({
  name: [{ required: true, message: '请输入名称' }],
  ua_ids: [{ required: true, message: '请选择UA' }]
})

const filterMethod = (query, item) => {
  return item.label.toLowerCase().includes(query.toLowerCase())
}

const uaList = ref([])

const getUaList = async () => {
  const res = await getUaListApi({
    paging: 0
  })
  uaList.value = res.data.map((item) => ({
    key: item.id,
    label: item.name
  }))
}

const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (form.value.id) {
        await putUaPoolApi(form.value.id, form.value)
      } else {
        await postUaPoolApi(form.value)
      }
      visible.value = false
      ElMessage.success('操作成功')
      emit('success')
    }
  })
}

const open = (data = {}) => {
  getUaList()

  form.value = {
    ...data,
    ua_ids: data?.uas?.map((item) => item.id) || []
  }
  formRef.value?.resetFields()
  visible.value = true
}

defineExpose({
  open
})
</script>
