import request from '@/axios'

export const getProxyPoolListApi = (params) => {
  return request.get({ url: '/proxyPool', params })
}

export const getProxyPoolDetailApi = (id) => {
  return request.get({ url: `/proxyPool/${id}` })
}

export const postProxyPoolApi = (data) => {
  return request.post({ url: '/proxyPool', data })
}

export const putProxyPoolApi = (id, data) => {
  return request.put({ url: `/proxyPool/${id}`, data })
}

export const deleteProxyPoolApi = (id) => {
  return request.delete({ url: `/proxyPool/${id}` })
}
