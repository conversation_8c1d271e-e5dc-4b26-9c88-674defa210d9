import request from '@/axios'

export const getUaListApi = (params) => {
  return request.get({ url: '/ua', params })
}

export const getUaDetailApi = (id) => {
  return request.get({ url: `/ua/${id}` })
}

export const postUaApi = (data) => {
  return request.post({ url: '/ua', data })
}

export const putUaApi = (id, data) => {
  return request.put({ url: `/ua/${id}`, data })
}

export const deleteUaApi = (id) => {
  return request.delete({ url: `/ua/${id}` })
}
