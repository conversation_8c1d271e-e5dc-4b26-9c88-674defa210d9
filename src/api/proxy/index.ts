import request from '@/axios'

export const getProxyListApi = (params) => {
  return request.get({ url: '/proxy', params })
}

export const getProxyDetailApi = (id) => {
  return request.get({ url: `/proxy/${id}` })
}

export const postProxyApi = (data) => {
  return request.post({ url: '/proxy', data })
}

export const putProxyApi = (id, data) => {
  return request.put({ url: `/proxy/${id}`, data })
}

export const deleteProxyApi = (id) => {
  return request.delete({ url: `/proxy/${id}` })
}

export const postProxyBatchApi = (data) => {
  return request.post({ url: '/proxy/batch/save', data })
}

export const postIpInfoApi = (data) => {
  return request.post({ url: '/proxy/ip/info', data })
}
