import request from '@/axios'

export const getUaPoolListApi = (params) => {
  return request.get({ url: '/uaPool', params })
}

export const getUaPoolDetailApi = (id) => {
  return request.get({ url: `/uaPool/${id}` })
}

export const postUaPoolApi = (data) => {
  return request.post({ url: '/uaPool', data })
}

export const putUaPoolApi = (id, data) => {
  return request.put({ url: `/uaPool/${id}`, data })
}

export const deleteUaPoolApi = (id) => {
  return request.delete({ url: `/uaPool/${id}` })
}
